#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <filesystem>
#include <map>
#include <vector>
#include <spdlog/spdlog.h>

namespace sco {

class CacheCommand : public BaseCommand {
public:
    CacheCommand() = default;
    
    int execute() override {
        try {
            if (action_.empty() || action_ == "show") {
                return show_cache();
            } else if (action_ == "rm") {
                return clear_cache();
            } else {
                std::cout << "Unknown cache action: " << action_ << "\n";
                std::cout << "Available actions: show, rm\n";
                return 1;
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Cache command failed: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "cache"; }
    std::string get_description() const override { return "Show or clear the download cache"; }
    
    void set_action(const std::string& action) { action_ = action; }
    void set_app_name(const std::string& app_name) { app_name_ = app_name; }
    
private:
    std::string action_;
    std::string app_name_;
    
    int show_cache() {
        auto& config = Config::instance();
        config.load();
        auto cache_dir = config.get_cache_dir();
        
        if (!std::filesystem::exists(cache_dir)) {
            SPDLOG_INFO("Cache directory does not exist: {}", cache_dir.string());
            return 0;
        }

        SPDLOG_INFO("Cache directory: {}", cache_dir.string());
        SPDLOG_INFO("");
        
        std::uintmax_t total_size = 0;
        int file_count = 0;
        
        try {
            // Group files by app
            std::map<std::string, std::vector<std::filesystem::directory_entry>> app_files;
            
            for (const auto& entry : std::filesystem::directory_iterator(cache_dir)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    
                    // Extract app name from filename (usually app#version#url_hash.ext)
                    std::string app_name = "unknown";
                    size_t hash_pos = filename.find('#');
                    if (hash_pos != std::string::npos) {
                        app_name = filename.substr(0, hash_pos);
                    }
                    
                    app_files[app_name].push_back(entry);
                    total_size += entry.file_size();
                    file_count++;
                }
            }
            
            if (file_count == 0) {
                SPDLOG_INFO("Cache is empty.");
                return 0;
            }

            // Display files grouped by app
            for (const auto& [app, files] : app_files) {
                SPDLOG_INFO("{}:", app);

                std::uintmax_t app_size = 0;
                for (const auto& file : files) {
                    auto size = file.file_size();
                    app_size += size;

                    SPDLOG_INFO("  {} ({})", file.path().filename().string(), format_bytes(size));
                }

                SPDLOG_INFO("  Total: {}", format_bytes(app_size));
                SPDLOG_INFO("");
            }

            SPDLOG_INFO("Cache summary:");
            SPDLOG_INFO("  Files: {}", file_count);
            SPDLOG_INFO("  Total size: {}", format_bytes(total_size));
            
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Filesystem error: {}", e.what());
            return 1;
        }
        
        return 0;
    }
    
    int clear_cache() {
        auto& config = Config::instance();
        config.load();
        auto cache_dir = config.get_cache_dir();
        
        if (!std::filesystem::exists(cache_dir)) {
            SPDLOG_INFO("Cache directory does not exist.");
            return 0;
        }
        
        try {
            int removed_count = 0;
            std::uintmax_t removed_size = 0;
            
            if (app_name_.empty() || app_name_ == "*") {
                // Remove all cache files
                for (const auto& entry : std::filesystem::directory_iterator(cache_dir)) {
                    if (entry.is_regular_file()) {
                        removed_size += entry.file_size();
                        std::filesystem::remove(entry.path());
                        removed_count++;
                    }
                }
                
                SPDLOG_INFO("Removed {} files ({}) from cache.", removed_count, format_bytes(removed_size));
            } else {
                // Remove cache files for specific app
                for (const auto& entry : std::filesystem::directory_iterator(cache_dir)) {
                    if (entry.is_regular_file()) {
                        std::string filename = entry.path().filename().string();
                        
                        // Check if filename starts with app name
                        if (filename.find(app_name_ + "#") == 0) {
                            removed_size += entry.file_size();
                            std::filesystem::remove(entry.path());
                            removed_count++;
                        }
                    }
                }
                
                if (removed_count > 0) {
                    SPDLOG_INFO("Removed {} files ({}) for {}.", removed_count, format_bytes(removed_size), app_name_);
                } else {
                    SPDLOG_INFO("No cache files found for {}.", app_name_);
                }
            }
            
        } catch (const std::filesystem::filesystem_error& e) {
            spdlog::error("Filesystem error: {}", e.what());
            return 1;
        }
        
        return 0;
    }
    
    std::string format_bytes(std::uintmax_t bytes) {
        const char* units[] = {"B", "KB", "MB", "GB", "TB"};
        int unit = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024.0 && unit < 4) {
            size /= 1024.0;
            unit++;
        }
        
        char buffer[32];
        if (unit == 0) {
            snprintf(buffer, sizeof(buffer), "%.0f %s", size, units[unit]);
        } else {
            snprintf(buffer, sizeof(buffer), "%.1f %s", size, units[unit]);
        }
        
        return std::string(buffer);
    }
};

} // namespace sco
