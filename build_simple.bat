@echo off
echo Building simple test without external dependencies...

REM Try different compilers
where cl >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo Using MSVC compiler...
    cl /std:c++17 /EHsc test_main.cpp /Fe:test_main.exe
    goto :run_test
)

where g++ >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo Using GCC compiler...
    g++ -std=c++17 test_main.cpp -o test_main.exe
    goto :run_test
)

where clang++ >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo Using Clang compiler...
    clang++ -std=c++17 test_main.cpp -o test_main.exe
    goto :run_test
)

echo No C++ compiler found!
echo Please install one of the following:
echo - Visual Studio (with C++ tools)
echo - MinGW-w64
echo - Clang
goto :end

:run_test
if exist test_main.exe (
    echo.
    echo Running test...
    test_main.exe
    echo.
    echo Test completed!
) else (
    echo Build failed!
)

:end
pause
