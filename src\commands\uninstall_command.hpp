#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/uninstall_manager.hpp"
#include "../utils/table_formatter.hpp"
#include "../utils/registry_cleaner.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <spdlog/spdlog.h>

namespace sco {

class UninstallCommand : public BaseCommand {
public:
    UninstallCommand() = default;
    
    int execute() override {
        try {
            if (apps_.empty()) {
                std::cerr << "No apps specified for uninstallation.\n";
                std::cout << "Usage: sco uninstall <app1> [app2] [app3] ...\n";
                std::cout << "       sco uninstall --global <app1> [app2] ...\n";
                std::cout << "       sco uninstall --force <app1> [app2] ...\n";
                std::cout << "       sco uninstall --purge <app1> [app2] ...\n";
                return 1;
            }
            
            SPDLOG_DEBUG("Uninstall command called with {} apps", apps_.size());
            
            // Prepare uninstall options
            UninstallManager::UninstallOptions options;
            options.global = global_;
            options.force = force_;
            options.purge = purge_;
            
            // Show what we're about to uninstall
            if (!show_uninstall_summary()) {
                return 1;
            }
            
            // Perform uninstallation
            auto start_time = std::chrono::steady_clock::now();
            auto result = UninstallManager::uninstall_apps(apps_, options);
            auto end_time = std::chrono::steady_clock::now();
            
            // Show results
            show_uninstall_results(result);
            
            return result.success ? 0 : 1;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Uninstall command failed: {}", e.what());
            std::cerr << "Uninstallation failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "uninstall"; }
    std::string get_description() const override { return "Uninstall apps"; }
    
    // Setters for command options
    void set_apps(const std::vector<std::string>& apps) { apps_ = apps; }
    void set_global(bool global) { global_ = global; }
    void set_force(bool force) { force_ = force; }
    void set_purge(bool purge) { purge_ = purge; }
    
private:
    std::vector<std::string> apps_;
    bool global_ = false;
    bool force_ = false;
    bool purge_ = false;
    
    bool show_uninstall_summary() {
        std::cout << "Uninstalling " << apps_.size() << " app(s):\n";
        
        // Check which apps are actually installed
        std::vector<std::string> installed_apps;
        std::vector<std::string> not_installed_apps;
        
        for (const auto& app : apps_) {
            if (UninstallManager::is_app_installed(app)) {
                installed_apps.push_back(app);
            } else {
                not_installed_apps.push_back(app);
            }
        }
        
        if (!installed_apps.empty()) {
            std::cout << "\nInstalled apps to uninstall:\n";
            for (const auto& app : installed_apps) {
                std::cout << "  - " << app << "\n";
            }
        }
        
        if (!not_installed_apps.empty()) {
            std::cout << "\nApps not installed (will be skipped):\n";
            for (const auto& app : not_installed_apps) {
                std::cout << "  - " << app << "\n";
            }
        }
        
        if (installed_apps.empty()) {
            std::cout << "\nNo installed apps to uninstall.\n";
            return false;
        }
        
        // Check for dependents
        if (!force_) {
            auto dependents = UninstallManager::get_dependents(installed_apps);
            if (!dependents.empty()) {
                std::cout << "\n⚠ Warning: The following apps depend on the apps you want to uninstall:\n";
                for (const auto& dependent : dependents) {
                    std::cout << "  - " << dependent << "\n";
                }
                std::cout << "\nUse --force to uninstall anyway, or uninstall the dependent apps first.\n";
                return false;
            }
        }
        
        if (global_) {
            std::cout << "\nUninstallation mode: Global\n";
        } else {
            std::cout << "\nUninstallation mode: User\n";
        }
        
        if (force_) {
            std::cout << "Force uninstall: Yes (ignoring dependents)\n";
        }
        
        if (purge_) {
            std::cout << "Purge data: Yes (will remove all app data)\n";
        }
        
        // Show what will be cleaned
        show_cleanup_preview(installed_apps);
        
        std::cout << "\n";
        return true;
    }
    
    void show_cleanup_preview(const std::vector<std::string>& apps) {
        std::cout << "\nThe following will be removed:\n";
        
        for (const auto& app : apps) {
            std::cout << "\nFor app '" << app << "':\n";
            
            // Show app directory
            auto& config = Config::instance();
            auto app_dir = config.get_apps_dir() / app;
            std::cout << "  - App directory: " << app_dir.string() << "\n";
            
            // Show shims
            auto shims = ShimManager::list_shims();
            bool has_shims = false;
            for (const auto& shim : shims) {
                if (shim.target_path.string().find(app) != std::string::npos) {
                    if (!has_shims) {
                        std::cout << "  - Shims:\n";
                        has_shims = true;
                    }
                    std::cout << "    * " << shim.name << ".exe\n";
                }
            }
            
            // Show registry entries (if supported)
            if (RegistryCleaner::is_registry_cleaning_supported()) {
                auto registry_preview = RegistryCleaner::get_cleanup_preview(app);
                if (!registry_preview.empty()) {
                    std::cout << "  - Registry entries:\n";
                    for (const auto& entry : registry_preview) {
                        std::cout << "    * " << entry << "\n";
                    }
                }
            }
        }
    }
    
    void show_uninstall_results(const UninstallManager::UninstallResult& result) {
        std::cout << "\n";
        std::cout << "Uninstallation completed in " << result.total_duration.count() << "ms\n";
        std::cout << "\n";
        
        if (result.success) {
            std::cout << "✓ Uninstallation successful!\n";
        } else {
            std::cout << "✗ Uninstallation failed!\n";
            if (!result.error_message.empty()) {
                std::cout << "Error: " << result.error_message << "\n";
            }
        }
        
        if (!result.uninstalled_apps.empty()) {
            std::cout << "\nSuccessfully uninstalled:\n";
            for (const auto& app : result.uninstalled_apps) {
                std::cout << "  ✓ " << app << "\n";
            }
        }
        
        if (!result.failed_apps.empty()) {
            std::cout << "\nFailed to uninstall:\n";
            for (const auto& app : result.failed_apps) {
                std::cout << "  ✗ " << app << "\n";
            }
        }
        
        if (!result.skipped_apps.empty()) {
            std::cout << "\nSkipped apps:\n";
            for (const auto& app : result.skipped_apps) {
                std::cout << "  - " << app << "\n";
            }
        }
        
        // Show post-uninstallation information
        if (result.success && !result.uninstalled_apps.empty()) {
            show_post_uninstall_info();
        }
    }
    
    void show_post_uninstall_info() {
        std::cout << "\nPost-uninstallation notes:\n";
        
        // Check if there are any remaining shims
        auto remaining_shims = ShimManager::list_shims();
        if (remaining_shims.empty()) {
            std::cout << "  All shims have been removed.\n";
        } else {
            std::cout << "  " << remaining_shims.size() << " shim(s) remain from other apps.\n";
        }
        
        // Show usage examples
        std::cout << "\nUsage examples:\n";
        std::cout << "  sco list                    # List remaining installed apps\n";
        std::cout << "  sco search <query>          # Search for new apps to install\n";
        std::cout << "  sco install <app>           # Install an app\n";
        std::cout << "  sco cleanup                 # Clean up cache and temporary files\n";
    }
    
public:
    // Static helper methods for other commands to use
    static bool is_app_installed(const std::string& app_name) {
        return UninstallManager::is_app_installed(app_name);
    }
    
    static std::vector<std::string> get_installed_apps() {
        return UninstallManager::get_installed_apps();
    }
    
    // Validate apps before uninstallation
    static bool validate_apps(const std::vector<std::string>& app_names, 
                            std::vector<std::string>& not_installed_apps) {
        not_installed_apps.clear();
        
        for (const auto& app_name : app_names) {
            if (!is_app_installed(app_name)) {
                not_installed_apps.push_back(app_name);
            }
        }
        
        return not_installed_apps.empty();
    }
    
    // Show dependency information
    static void show_dependency_info(const std::vector<std::string>& app_names) {
        auto dependents = UninstallManager::get_dependents(app_names);
        
        if (dependents.empty()) {
            std::cout << "No other apps depend on the specified apps.\n";
            std::cout << "Safe to uninstall.\n";
            return;
        }
        
        std::cout << "The following apps depend on the specified apps:\n";
        for (const auto& dependent : dependents) {
            std::cout << "  " << dependent << "\n";
        }
        
        std::cout << "\nYou should uninstall the dependent apps first, or use --force to override.\n";
    }
    
    // Preview what would be uninstalled without actually doing it
    static void preview_uninstall(const std::vector<std::string>& app_names) {
        std::cout << "Uninstall preview for " << app_names.size() << " app(s):\n\n";
        
        for (const auto& app_name : app_names) {
            if (!is_app_installed(app_name)) {
                std::cout << "App '" << app_name << "' is not installed.\n\n";
                continue;
            }
            
            std::cout << "App: " << app_name << "\n";
            
            // Show app directory
            auto& config = Config::instance();
            auto app_dir = config.get_apps_dir() / app_name;
            std::cout << "  Directory: " << app_dir.string() << "\n";
            
            // Show size if possible
            try {
                size_t total_size = 0;
                for (const auto& entry : std::filesystem::recursive_directory_iterator(app_dir)) {
                    if (entry.is_regular_file()) {
                        total_size += entry.file_size();
                    }
                }
                
                if (total_size > 1024 * 1024) {
                    std::cout << "  Size: " << (total_size / (1024 * 1024)) << " MB\n";
                } else if (total_size > 1024) {
                    std::cout << "  Size: " << (total_size / 1024) << " KB\n";
                } else {
                    std::cout << "  Size: " << total_size << " bytes\n";
                }
            } catch (const std::exception& e) {
                std::cout << "  Size: Unable to calculate\n";
            }
            
            // Show shims
            auto shims = ShimManager::list_shims();
            std::vector<std::string> app_shims;
            for (const auto& shim : shims) {
                if (shim.target_path.string().find(app_name) != std::string::npos) {
                    app_shims.push_back(shim.name);
                }
            }
            
            if (!app_shims.empty()) {
                std::cout << "  Shims: ";
                for (size_t i = 0; i < app_shims.size(); ++i) {
                    if (i > 0) std::cout << ", ";
                    std::cout << app_shims[i];
                }
                std::cout << "\n";
            }
            
            std::cout << "\n";
        }
        
        // Show dependents
        auto dependents = UninstallManager::get_dependents(app_names);
        if (!dependents.empty()) {
            std::cout << "⚠ Warning: The following apps depend on the apps you want to uninstall:\n";
            for (const auto& dependent : dependents) {
                std::cout << "  - " << dependent << "\n";
            }
            std::cout << "\n";
        }
    }
};

} // namespace sco
