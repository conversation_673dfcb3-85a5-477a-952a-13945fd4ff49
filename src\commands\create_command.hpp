#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <string>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace sco {

class CreateCommand : public BaseCommand {
public:
    CreateCommand() = default;
    
    int execute() override {
        try {
            if (app_name_.empty()) {
                std::cerr << "App name is required.\n";
                std::cout << "Usage: sco create <app_name>\n";
                return 1;
            }

            SPDLOG_DEBUG("Create command called for app: {}", app_name_);

            return create_manifest();

        } catch (const std::exception& e) {
            SPDLOG_ERROR("Create command failed: {}", e.what());
            std::cerr << "Create failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "create"; }
    std::string get_description() const override { return "Create a custom app manifest"; }
    
    void set_app_name(const std::string& app_name) { app_name_ = app_name; }
    void set_output_dir(const std::string& dir) { output_dir_ = dir; }
    
private:
    std::string app_name_;
    std::string output_dir_;
    
    int create_manifest() {
        std::cout << "Creating manifest for '" << app_name_ << "'...\n\n";
        
        // Determine output directory
        std::filesystem::path output_path;
        if (output_dir_.empty()) {
            output_path = std::filesystem::current_path();
        } else {
            output_path = output_dir_;
        }
        
        auto manifest_file = output_path / (app_name_ + ".json");
        
        // Check if file already exists
        if (std::filesystem::exists(manifest_file)) {
            std::cout << "Manifest file already exists: " << manifest_file.string() << "\n";
            std::cout << "Do you want to overwrite it? (y/N): ";
            
            std::string response;
            std::getline(std::cin, response);
            
            if (response != "y" && response != "Y") {
                std::cout << "Cancelled.\n";
                return 0;
            }
        }
        
        // Create manifest interactively
        auto manifest = create_manifest_interactively();
        
        // Write manifest to file
        try {
            std::filesystem::create_directories(output_path);
            
            std::ofstream file(manifest_file);
            if (!file.is_open()) {
                std::cerr << "Failed to create manifest file: " << manifest_file.string() << std::endl;
                return 1;
            }
            
            file << manifest.dump(4) << std::endl;
            file.close();
            
            std::cout << "\nManifest created successfully: " << manifest_file.string() << "\n";
            std::cout << "\nNext steps:\n";
            std::cout << "1. Review and edit the manifest file as needed\n";
            std::cout << "2. Test the manifest: sco install " << manifest_file.string() << "\n";
            std::cout << "3. Add to a bucket or share with others\n";
            
            return 0;
            
        } catch (const std::exception& e) {
            std::cerr << "Failed to write manifest file: " << e.what() << std::endl;
            return 1;
        }
    }
    
    nlohmann::json create_manifest_interactively() {
        nlohmann::json manifest;
        
        std::cout << "Please provide the following information:\n";
        std::cout << "(Press Enter to skip optional fields)\n\n";
        
        // Basic information
        manifest["name"] = app_name_;
        
        std::cout << "Version: ";
        std::string version;
        std::getline(std::cin, version);
        if (version.empty()) version = "1.0.0";
        manifest["version"] = version;
        
        std::cout << "Description: ";
        std::string description;
        std::getline(std::cin, description);
        if (!description.empty()) {
            manifest["description"] = description;
        }
        
        std::cout << "Homepage URL (optional): ";
        std::string homepage;
        std::getline(std::cin, homepage);
        if (!homepage.empty()) {
            manifest["homepage"] = homepage;
        }
        
        std::cout << "License (optional): ";
        std::string license;
        std::getline(std::cin, license);
        if (!license.empty()) {
            manifest["license"] = license;
        }
        
        // Download URLs
        std::cout << "\nDownload URLs:\n";
        std::cout << "64-bit URL: ";
        std::string url_64;
        std::getline(std::cin, url_64);
        
        if (!url_64.empty()) {
            manifest["url"] = url_64;
            
            std::cout << "64-bit Hash (SHA256, optional): ";
            std::string hash_64;
            std::getline(std::cin, hash_64);
            if (!hash_64.empty()) {
                manifest["hash"] = hash_64;
            }
        }
        
        std::cout << "32-bit URL (optional): ";
        std::string url_32;
        std::getline(std::cin, url_32);
        
        if (!url_32.empty()) {
            nlohmann::json architecture;
            architecture["32bit"]["url"] = url_32;
            
            std::cout << "32-bit Hash (SHA256, optional): ";
            std::string hash_32;
            std::getline(std::cin, hash_32);
            if (!hash_32.empty()) {
                architecture["32bit"]["hash"] = hash_32;
            }
            
            manifest["architecture"] = architecture;
        }
        
        // Extraction
        std::cout << "\nExtraction settings:\n";
        std::cout << "Extract directory (optional, e.g., 'app-1.0'): ";
        std::string extract_dir;
        std::getline(std::cin, extract_dir);
        if (!extract_dir.empty()) {
            manifest["extract_dir"] = extract_dir;
        }
        
        // Executables
        std::cout << "\nExecutables:\n";
        std::cout << "Main executable (e.g., 'app.exe'): ";
        std::string main_exe;
        std::getline(std::cin, main_exe);
        
        if (!main_exe.empty()) {
            manifest["bin"] = nlohmann::json::array({main_exe});
            
            std::cout << "Additional executables (comma-separated, optional): ";
            std::string additional_exes;
            std::getline(std::cin, additional_exes);
            
            if (!additional_exes.empty()) {
                auto bin_array = manifest["bin"];
                
                // Parse comma-separated list
                std::stringstream ss(additional_exes);
                std::string exe;
                while (std::getline(ss, exe, ',')) {
                    // Trim whitespace
                    exe.erase(0, exe.find_first_not_of(" \t"));
                    exe.erase(exe.find_last_not_of(" \t") + 1);
                    if (!exe.empty()) {
                        bin_array.push_back(exe);
                    }
                }
                
                manifest["bin"] = bin_array;
            }
        }
        
        // Shortcuts
        std::cout << "\nShortcuts:\n";
        std::cout << "Create desktop shortcut? (y/N): ";
        std::string create_shortcut;
        std::getline(std::cin, create_shortcut);
        
        if (create_shortcut == "y" || create_shortcut == "Y") {
            if (!main_exe.empty()) {
                manifest["shortcuts"] = nlohmann::json::array({
                    nlohmann::json::array({main_exe, app_name_})
                });
            }
        }
        
        // Dependencies
        std::cout << "\nDependencies (comma-separated, optional): ";
        std::string dependencies;
        std::getline(std::cin, dependencies);
        
        if (!dependencies.empty()) {
            nlohmann::json deps = nlohmann::json::array();
            
            std::stringstream ss(dependencies);
            std::string dep;
            while (std::getline(ss, dep, ',')) {
                // Trim whitespace
                dep.erase(0, dep.find_first_not_of(" \t"));
                dep.erase(dep.find_last_not_of(" \t") + 1);
                if (!dep.empty()) {
                    deps.push_back(dep);
                }
            }
            
            manifest["depends"] = deps;
        }
        
        // Notes
        std::cout << "\nNotes (optional): ";
        std::string notes;
        std::getline(std::cin, notes);
        if (!notes.empty()) {
            manifest["notes"] = notes;
        }
        
        return manifest;
    }
};

} // namespace sco
