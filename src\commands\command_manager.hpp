#pragma once

#include <memory>
#include <unordered_map>
#include <functional>
#include <sstream>
#include <CLI/CLI.hpp>
#include <spdlog/spdlog.h>

#include "base_command.hpp"
#include "help_command.hpp"
#include "list_command.hpp"
#include "config_command.hpp"
#include "checkup_command.hpp"
#include "status_command.hpp"
#include "cache_command.hpp"
#include "search_command.hpp"
#include "install_command.hpp"
#include "uninstall_command.hpp"
#include "update_command.hpp"
#include "info_command.hpp"
#include "home_command.hpp"
#include "bucket_command.hpp"
#include "cat_command.hpp"
#include "prefix_command.hpp"
#include "which_command.hpp"
#include "depends_command.hpp"

namespace sco {

// Helper function to join strings
template<typename Container>
std::string join_strings(const Container& container, const std::string& delimiter) {
    if (container.empty()) return "";

    std::ostringstream oss;
    auto it = container.begin();
    oss << *it;
    ++it;

    for (; it != container.end(); ++it) {
        oss << delimiter << *it;
    }

    return oss.str();
}

class CommandManager {
public:
    CommandManager() {
        // Commands will be registered when register_commands is called
    }
    
    ~CommandManager() = default;
    
    void register_commands(CLI::App& app) {
        register_help_command(app);
        register_list_command(app);
        register_install_command(app);
        register_uninstall_command(app);
        register_update_command(app);
        register_search_command(app);
        register_info_command(app);
        register_home_command(app);
        register_bucket_command(app);
        register_config_command(app);
        register_cache_command(app);
        register_cat_command(app);
        register_checkup_command(app);
        register_cleanup_command(app);
        register_create_command(app);
        register_depends_command(app);
        register_download_command(app);
        register_export_command(app);
        register_import_command(app);
        register_hold_command(app);
        register_unhold_command(app);
        register_prefix_command(app);
        register_reset_command(app);
        register_shim_command(app);
        register_status_command(app);
        register_virustotal_command(app);
        register_which_command(app);
        register_alias_command(app);
    }
    
    int execute() {
        if (current_command_) {
            return current_command_();
        }
        // No command was selected, execute help command
        return execute_help_command();
    }

    int execute_help_command() {
        auto it = commands_.find("help");
        if (it != commands_.end()) {
            return it->second->execute();
        }
        return 0;
    }
    
private:
    std::unordered_map<std::string, std::unique_ptr<BaseCommand>> commands_;
    std::function<int()> current_command_;
    
    void register_command(const std::string& name, std::unique_ptr<BaseCommand> command) {
        commands_[name] = std::move(command);
    }
    
    void register_help_command(CLI::App& app) {
        auto help_cmd = std::make_unique<HelpCommand>();
        help_cmd->set_app(&app);
        
        auto* sub = app.add_subcommand("help", "Show help for a command");
        std::string command_name;
        sub->add_option("command", command_name, "Command to show help for");
        
        sub->callback([this, help_cmd_ptr = help_cmd.get(), command_name]() {
            help_cmd_ptr->set_command_name(command_name);
            current_command_ = [help_cmd_ptr]() { return help_cmd_ptr->execute(); };
        });
        
        register_command("help", std::move(help_cmd));
    }
    
    void register_list_command(CLI::App& app) {
        auto list_cmd = std::make_unique<ListCommand>();
        
        auto* sub = app.add_subcommand("list", "List installed apps");
        
        sub->callback([this, list_cmd_ptr = list_cmd.get()]() {
            current_command_ = [list_cmd_ptr]() { return list_cmd_ptr->execute(); };
        });
        
        register_command("list", std::move(list_cmd));
    }
    
    void register_install_command(CLI::App& app) {
        auto install_cmd = std::make_unique<InstallCommand>();
        auto install_cmd_ptr = install_cmd.get();

        auto* sub = app.add_subcommand("install", "Install apps");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto global = std::make_shared<bool>(false);
        auto skip_deps = std::make_shared<bool>(false);
        auto force = std::make_shared<bool>(false);
        auto no_cache = std::make_shared<bool>(false);
        auto arch = std::make_shared<std::string>("64bit");

        sub->add_option("apps", *apps, "Apps to install")->required();
        sub->add_flag("--global,-g", *global, "Install globally");
        sub->add_flag("--skip-dependencies", *skip_deps, "Skip dependency resolution");
        sub->add_flag("--force,-f", *force, "Force reinstall");
        sub->add_flag("--no-cache", *no_cache, "Don't use cache");
        sub->add_option("--arch,-a", *arch, "Architecture (32bit, 64bit, arm64)");

        sub->callback([this, install_cmd_ptr, apps, global, skip_deps, force, no_cache, arch]() {
            install_cmd_ptr->set_apps(*apps);
            install_cmd_ptr->set_global(*global);
            install_cmd_ptr->set_skip_dependencies(*skip_deps);
            install_cmd_ptr->set_force(*force);
            install_cmd_ptr->set_no_cache(*no_cache);
            install_cmd_ptr->set_architecture(*arch);
            current_command_ = [install_cmd_ptr]() { return install_cmd_ptr->execute(); };
        });

        register_command("install", std::move(install_cmd));
    }
    
    void register_uninstall_command(CLI::App& app) {
        auto uninstall_cmd = std::make_unique<UninstallCommand>();
        auto uninstall_cmd_ptr = uninstall_cmd.get();

        auto* sub = app.add_subcommand("uninstall", "Uninstall apps");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto global = std::make_shared<bool>(false);
        auto force = std::make_shared<bool>(false);
        auto purge = std::make_shared<bool>(false);

        sub->add_option("apps", *apps, "Apps to uninstall")->required();
        sub->add_flag("--global,-g", *global, "Uninstall globally");
        sub->add_flag("--force,-f", *force, "Force uninstall (ignore dependents)");
        sub->add_flag("--purge,-p", *purge, "Remove all app data");

        sub->callback([this, uninstall_cmd_ptr, apps, global, force, purge]() {
            uninstall_cmd_ptr->set_apps(*apps);
            uninstall_cmd_ptr->set_global(*global);
            uninstall_cmd_ptr->set_force(*force);
            uninstall_cmd_ptr->set_purge(*purge);
            current_command_ = [uninstall_cmd_ptr]() { return uninstall_cmd_ptr->execute(); };
        });

        register_command("uninstall", std::move(uninstall_cmd));
    }
    
    void register_update_command(CLI::App& app) {
        auto update_cmd = std::make_unique<UpdateCommand>();
        auto update_cmd_ptr = update_cmd.get();

        auto* sub = app.add_subcommand("update", "Update apps, or Scoop itself");
        auto apps = std::make_shared<std::vector<std::string>>();
        auto all = std::make_shared<bool>(false);
        auto global = std::make_shared<bool>(false);
        auto force = std::make_shared<bool>(false);
        auto no_cache = std::make_shared<bool>(false);
        auto skip_deps = std::make_shared<bool>(false);

        sub->add_option("apps", *apps, "Apps to update");
        sub->add_flag("--all,-a", *all, "Update all apps");
        sub->add_flag("--global,-g", *global, "Update globally installed apps");
        sub->add_flag("--force,-f", *force, "Force update even if versions match");
        sub->add_flag("--no-cache", *no_cache, "Don't use cache");
        sub->add_flag("--skip-dependencies", *skip_deps, "Skip dependency resolution");

        sub->callback([this, update_cmd_ptr, apps, all, global, force, no_cache, skip_deps]() {
            update_cmd_ptr->set_apps(*apps);
            update_cmd_ptr->set_update_all(*all);
            update_cmd_ptr->set_global(*global);
            update_cmd_ptr->set_force(*force);
            update_cmd_ptr->set_no_cache(*no_cache);
            update_cmd_ptr->set_skip_dependencies(*skip_deps);
            current_command_ = [update_cmd_ptr]() { return update_cmd_ptr->execute(); };
        });

        register_command("update", std::move(update_cmd));
    }
    
    void register_search_command(CLI::App& app) {
        auto search_cmd = std::make_unique<SearchCommand>();
        auto search_cmd_ptr = search_cmd.get();

        auto* sub = app.add_subcommand("search", "Search available apps");
        auto query = std::make_shared<std::string>();
        sub->add_option("query", *query, "Search query")->required();

        sub->callback([this, search_cmd_ptr, query]() {
            search_cmd_ptr->set_query(*query);
            current_command_ = [search_cmd_ptr]() { return search_cmd_ptr->execute(); };
        });

        register_command("search", std::move(search_cmd));
    }
    
    void register_info_command(CLI::App& app) {
        auto info_cmd = std::make_unique<InfoCommand>();
        auto info_cmd_ptr = info_cmd.get();

        auto* sub = app.add_subcommand("info", "Display information about an app");
        auto app_name = std::make_shared<std::string>();
        sub->add_option("app", *app_name, "App name")->required();

        sub->callback([this, info_cmd_ptr, app_name]() {
            info_cmd_ptr->set_app_name(*app_name);
            current_command_ = [info_cmd_ptr]() { return info_cmd_ptr->execute(); };
        });

        register_command("info", std::move(info_cmd));
    }
    
    void register_home_command(CLI::App& app) {
        auto home_cmd = std::make_unique<HomeCommand>();
        auto home_cmd_ptr = home_cmd.get();

        auto* sub = app.add_subcommand("home", "Opens the app homepage");
        auto app_names = std::make_shared<std::vector<std::string>>();
        sub->add_option("apps", *app_names, "App names")->required();

        sub->callback([this, home_cmd_ptr, app_names]() {
            home_cmd_ptr->set_app_names(*app_names);
            current_command_ = [home_cmd_ptr]() { return home_cmd_ptr->execute(); };
        });

        register_command("home", std::move(home_cmd));
    }

    // Placeholder implementations for remaining commands
    void register_bucket_command(CLI::App& app) {
        auto bucket_cmd = std::make_unique<BucketCommand>();
        auto bucket_cmd_ptr = bucket_cmd.get();

        auto* sub = app.add_subcommand("bucket", "Manage Scoop buckets");
        auto action = std::make_shared<std::string>();
        auto bucket_name = std::make_shared<std::string>();
        auto bucket_url = std::make_shared<std::string>();

        sub->add_option("action", *action, "Action: list, add, remove, known");
        sub->add_option("name", *bucket_name, "Bucket name");
        sub->add_option("url", *bucket_url, "Bucket URL (for add action)");

        sub->callback([this, bucket_cmd_ptr, action, bucket_name, bucket_url]() {
            bucket_cmd_ptr->set_action(*action);
            bucket_cmd_ptr->set_bucket_name(*bucket_name);
            bucket_cmd_ptr->set_bucket_url(*bucket_url);
            current_command_ = [bucket_cmd_ptr]() { return bucket_cmd_ptr->execute(); };
        });

        register_command("bucket", std::move(bucket_cmd));
    }

    void register_config_command(CLI::App& app) {
        auto config_cmd = std::make_unique<ConfigCommand>();

        auto* sub = app.add_subcommand("config", "Get or set configuration values");
        std::string key, value;
        sub->add_option("key", key, "Configuration key");
        sub->add_option("value", value, "Configuration value");

        sub->callback([this, config_cmd_ptr = config_cmd.get(), key, value]() {
            config_cmd_ptr->set_key(key);
            config_cmd_ptr->set_value(value);
            current_command_ = [config_cmd_ptr]() { return config_cmd_ptr->execute(); };
        });

        register_command("config", std::move(config_cmd));
    }

    void register_cache_command(CLI::App& app) {
        auto cache_cmd = std::make_unique<CacheCommand>();

        auto* sub = app.add_subcommand("cache", "Show or clear the download cache");
        std::string action, app_name;
        sub->add_option("action", action, "Action: show, rm");
        sub->add_option("app", app_name, "App name (for rm action)");

        sub->callback([this, cache_cmd_ptr = cache_cmd.get(), action, app_name]() {
            cache_cmd_ptr->set_action(action);
            cache_cmd_ptr->set_app_name(app_name);
            current_command_ = [cache_cmd_ptr]() { return cache_cmd_ptr->execute(); };
        });

        register_command("cache", std::move(cache_cmd));
    }

    void register_cat_command(CLI::App& app) {
        auto cat_cmd = std::make_unique<CatCommand>();
        auto cat_cmd_ptr = cat_cmd.get();

        auto* sub = app.add_subcommand("cat", "Show content of specified manifest");
        auto app_name = std::make_shared<std::string>();
        sub->add_option("app", *app_name, "App name")->required();

        sub->callback([this, cat_cmd_ptr, app_name]() {
            cat_cmd_ptr->set_app_name(*app_name);
            current_command_ = [cat_cmd_ptr]() { return cat_cmd_ptr->execute(); };
        });

        register_command("cat", std::move(cat_cmd));
    }

    void register_checkup_command(CLI::App& app) {
        auto checkup_cmd = std::make_unique<CheckupCommand>();

        auto* sub = app.add_subcommand("checkup", "Check for potential problems");

        sub->callback([this, checkup_cmd_ptr = checkup_cmd.get()]() {
            current_command_ = [checkup_cmd_ptr]() { return checkup_cmd_ptr->execute(); };
        });

        register_command("checkup", std::move(checkup_cmd));
    }

    void register_cleanup_command(CLI::App& app) {
        auto* sub = app.add_subcommand("cleanup", "Cleanup apps by removing old versions");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Cleanup command not yet implemented.\n";
                return 0;
            };
        });
    }

    void register_create_command(CLI::App& app) {
        auto* sub = app.add_subcommand("create", "Create a custom app manifest");
        std::string app_name;
        sub->add_option("app", app_name, "App name")->required();

        sub->callback([this, app_name]() {
            current_command_ = [app_name]() {
                std::cout << "Create command not yet implemented.\n";
                std::cout << "App: " << app_name << "\n";
                return 0;
            };
        });
    }

    void register_depends_command(CLI::App& app) {
        auto depends_cmd = std::make_unique<DependsCommand>();
        auto depends_cmd_ptr = depends_cmd.get();

        auto* sub = app.add_subcommand("depends", "List dependencies for an app");
        auto app_name = std::make_shared<std::string>();
        auto show_tree = std::make_shared<bool>(false);

        sub->add_option("app", *app_name, "App name")->required();
        sub->add_flag("--tree,-t", *show_tree, "Show dependency tree");

        sub->callback([this, depends_cmd_ptr, app_name, show_tree]() {
            depends_cmd_ptr->set_app_name(*app_name);
            depends_cmd_ptr->set_show_tree(*show_tree);
            current_command_ = [depends_cmd_ptr]() { return depends_cmd_ptr->execute(); };
        });

        register_command("depends", std::move(depends_cmd));
    }

    void register_download_command(CLI::App& app) {
        auto* sub = app.add_subcommand("download", "Download apps in the cache folder and verify hashes");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to download")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "Download command not yet implemented.\n";
                std::cout << "Apps to download: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_export_command(CLI::App& app) {
        auto* sub = app.add_subcommand("export", "Exports installed apps, buckets (and optionally configs) in JSON format");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Export command not yet implemented.\n";
                return 0;
            };
        });
    }

    void register_import_command(CLI::App& app) {
        auto* sub = app.add_subcommand("import", "Imports apps, buckets and configs from a Scoopfile in JSON format");
        std::string file;
        sub->add_option("file", file, "JSON file to import")->required();

        sub->callback([this, file]() {
            current_command_ = [file]() {
                std::cout << "Import command not yet implemented.\n";
                std::cout << "File: " << file << "\n";
                return 0;
            };
        });
    }

    void register_hold_command(CLI::App& app) {
        auto* sub = app.add_subcommand("hold", "Hold an app to disable updates");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to hold")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "Hold command not yet implemented.\n";
                std::cout << "Apps to hold: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_unhold_command(CLI::App& app) {
        auto* sub = app.add_subcommand("unhold", "Unhold an app to enable updates");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to unhold")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "Unhold command not yet implemented.\n";
                std::cout << "Apps to unhold: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_prefix_command(CLI::App& app) {
        auto prefix_cmd = std::make_unique<PrefixCommand>();
        auto prefix_cmd_ptr = prefix_cmd.get();

        auto* sub = app.add_subcommand("prefix", "Returns the path to the specified app");
        auto app_name = std::make_shared<std::string>();
        sub->add_option("app", *app_name, "App name")->required();

        sub->callback([this, prefix_cmd_ptr, app_name]() {
            prefix_cmd_ptr->set_app_name(*app_name);
            current_command_ = [prefix_cmd_ptr]() { return prefix_cmd_ptr->execute(); };
        });

        register_command("prefix", std::move(prefix_cmd));
    }

    void register_reset_command(CLI::App& app) {
        auto* sub = app.add_subcommand("reset", "Reset an app to resolve conflicts");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to reset")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "Reset command not yet implemented.\n";
                std::cout << "Apps to reset: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_shim_command(CLI::App& app) {
        auto* sub = app.add_subcommand("shim", "Manipulate Scoop shims");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Shim command not yet implemented.\n";
                return 0;
            };
        });
    }

    void register_status_command(CLI::App& app) {
        auto status_cmd = std::make_unique<StatusCommand>();

        auto* sub = app.add_subcommand("status", "Show status and check for new app versions");

        sub->callback([this, status_cmd_ptr = status_cmd.get()]() {
            current_command_ = [status_cmd_ptr]() { return status_cmd_ptr->execute(); };
        });

        register_command("status", std::move(status_cmd));
    }

    void register_virustotal_command(CLI::App& app) {
        auto* sub = app.add_subcommand("virustotal", "Look for app's hash or url on virustotal.com");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to check")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "VirusTotal command not yet implemented.\n";
                std::cout << "Apps to check: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_which_command(CLI::App& app) {
        auto which_cmd = std::make_unique<WhichCommand>();
        auto which_cmd_ptr = which_cmd.get();

        auto* sub = app.add_subcommand("which", "Locate a shim/executable (similar to 'which' on Linux)");
        auto command = std::make_shared<std::string>();
        sub->add_option("command", *command, "Command to locate")->required();

        sub->callback([this, which_cmd_ptr, command]() {
            which_cmd_ptr->set_command_name(*command);
            current_command_ = [which_cmd_ptr]() { return which_cmd_ptr->execute(); };
        });

        register_command("which", std::move(which_cmd));
    }

    void register_alias_command(CLI::App& app) {
        auto* sub = app.add_subcommand("alias", "Manage scoop aliases");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Alias command not yet implemented.\n";
                return 0;
            };
        });
    }
};

} // namespace sco
